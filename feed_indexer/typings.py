from collections import defaultdict
from datetime import datetime
from enum import Enum
from typing import Annotated, Any, Literal

import utils_general
from datagrabber import CatalogAssetType, CatalogItem
from hexbytes import HexBytes
from pydantic import BaseModel, Field, field_validator, model_validator
from utils_oracle import FeedIndex, Network
from utils_oracle.db.table_models import FeedState

Index = int

# key by feed key for faster lookup
FeedKeyToIndexMap = dict[HexBytes, FeedIndex]
# stores the FeedStates of each index
ChainFeedState = defaultdict[FeedState, FeedKeyToIndexMap]
# chain_id -> FeedState -> FeedKey -> FeedIndex
FeedIndexerInternalFeeds = defaultdict[Network, ChainFeedState]

ChainIdGroupedFeedIndices = dict[Network, list[FeedIndex]]

CatalogItemsMap = dict[str, CatalogItem]
ChainCatalogItems = dict[Network, CatalogItemsMap]


class ExpiryRangeConfig(BaseModel):
    """Configuration for the expiry range of feeds.

    This class defines the minimum and maximum number of days from the current date
    that a feed's expiry date should fall within to be considered valid.
    """

    gte: float | None = Field(default=None, ge=0)
    lte: float | None = Field(default=None, ge=0)

    @model_validator(mode="after")
    def validate_days_comparison(self) -> "ExpiryRangeConfig":
        if self.gte is not None and self.lte is not None:
            if self.gte >= self.lte:
                raise ValueError(
                    f"{self.gte=} must be greater than {self.lte=}"
                )
        return self

    def is_tenor_within_range(
        self,
        listing_date: datetime | None = None,
        expiry_date: datetime | None = None,
        tenor_days: float | None = None,
    ) -> bool:
        """
        Check if the tenor falls within the configured range.

        Args:
            listing_date: The listing date (required if tenor is not provided)
            expiry_date: The expiry date (required if tenor is not provided)
            tenor: The tenor in days (if provided, listing_date and expiry_date are not needed)

        Returns:
            True if the tenor is within the range, False otherwise
        """
        if tenor_days is None:
            if listing_date is None or expiry_date is None:
                raise ValueError(
                    "Both listing_date and expiry_date must be provided when tenor is not specified"
                )
            tenor_days = (expiry_date - listing_date).total_seconds() / (
                24 * 3600
            )

        if self.gte is not None:
            if tenor_days < self.gte:
                utils_general.log_bsdebug(
                    "Tenor %s is less than minimum %s, rejecting",
                    tenor_days,
                    self.gte,
                )
                return False

        if self.lte is not None:
            if tenor_days > self.lte:
                utils_general.log_bsdebug(
                    "Tenor %s is greater than maximum %s, rejecting",
                    tenor_days,
                    self.lte,
                )
                return False

        utils_general.log_bsdebug(
            "Tenor %s is within range [%s, %s], accepting",
            tenor_days,
            self.gte,
            self.lte,
        )
        return True


class TargetFeed(BaseModel):
    asset_class: CatalogAssetType
    exchange: Annotated[list[str], Field(min_length=1)]
    base_asset: Annotated[list[str], Field(min_length=1)]
    constant_tenors: list[str] = Field(default_factory=list)
    listing_filter: ExpiryRangeConfig | None = None

    @model_validator(mode="after")
    def validate_option_future_only_fields(self) -> "TargetFeed":
        """Validate that listing_filter and constant_tenors are only used with option and future asset classes."""
        allowed_asset_classes = [
            "option",
            "future",
            "option-equity",
            "future-equity",
        ]

        if self.asset_class not in allowed_asset_classes:
            if self.listing_filter is not None:
                raise ValueError(
                    f"listing_filter can only be used with {allowed_asset_classes} asset classes"
                )
            if self.constant_tenors:
                raise ValueError(
                    f"constant_tenors can only be used with {allowed_asset_classes} asset classes"
                )

        return self


class TargetConfig(BaseModel):
    targets: list[TargetFeed]
    id: Network
    decimals: Annotated[int, Field(ge=0)]
    version: Annotated[int, Field(ge=0)]
    enable: bool

    @field_validator("id", mode="before")
    @classmethod
    def validate_network(cls, v: int) -> Network:
        return Network(v)


# AssetType -> currency -> list[tenor]
CurrencyToConstantTenors = dict[str, list[str]]
AssetTypeToCurrencyConstantTenors = dict[
    Literal["future", "option"], CurrencyToConstantTenors
]
ChainConstantTenorInfo = dict[Network, AssetTypeToCurrencyConstantTenors]


class AuxDetails(BaseModel):
    max_feed_index: int
    version: int
    decimals: int


ChainToAuxDetails = dict[Network, AuxDetails]


Targets = dict[str, TargetConfig]


class FeedIndexerMessageTypes(Enum):
    INDEX_ASSIGNMENT = "IndexAssignment"
    INDEX_REMOVAL = "IndexRemoval"


class FeedIndexerCatalogFilter(BaseModel):
    exchanges: list[str]
    base_assets: list[str]
    quote_assets: list[str] = Field(default_factory=list)
    constant_tenors: list[str] | None = None
    asset_class: CatalogAssetType
    # cannot currently be used to remove feeds on-chain
    listing_filter: ExpiryRangeConfig | None = None

    @model_validator(mode="after")
    def validate_option_future_only_fields(self) -> "FeedIndexerCatalogFilter":
        """Validate that listing_filter and constant_tenors are only used with option and future asset classes."""
        allowed_asset_classes = [
            "option",
            "future",
            "option-equity",
            "future-equity",
        ]

        if self.asset_class not in allowed_asset_classes:
            if self.listing_filter is not None:
                raise ValueError(
                    f"listing_filter can only be used with {allowed_asset_classes} asset classes"
                )
            if self.constant_tenors:
                raise ValueError(
                    f"constant_tenors can only be used with {allowed_asset_classes} asset classes"
                )

        return self

    def _check_basic_info(self, data: Any, qn_tokens: list[str]) -> bool:
        return not (
            qn_tokens[0] not in self.exchanges
            or qn_tokens[1] != self.asset_class
            or data.get("baseAsset") not in self.base_assets
        )

    def _check_quote_asset(self, data: Any) -> bool:
        return not (
            self.quote_assets
            and data.get("quoteAsset") not in self.quote_assets
        )

    def _check_constant_tenor(self, data: Any) -> bool:
        from feed_indexer.utils import is_iso_date

        return not (
            "expiry" in data
            and not is_iso_date(data["expiry"])
            and self.constant_tenors is not None
            and data["expiry"] not in self.constant_tenors
        )

    def _check_listing_filter(self, data: Any) -> bool:
        from feed_indexer.utils import is_iso_date

        if (
            self.listing_filter is not None
            and "expiry" in data
            and is_iso_date(data["expiry"])
        ):
            if "listing" not in data:
                raise ValueError(
                    "Catalog item with ISO expiry date must have a listing date"
                )

            expiry_date = utils_general.from_iso(data["expiry"])
            listing_date = utils_general.from_iso(data["listing"])
            return self.listing_filter.is_tenor_within_range(
                listing_date=listing_date,
                expiry_date=expiry_date,
            )
        return True

    def accept_catalog_item(self, data: Any) -> bool:
        """
        Check if the catalog item matches this filter's criteria.

        Args:
            data: The catalog item data
            qualified_name: The qualified name of the catalog item (e.g., "exchange.asset_class.contracts")

        Returns:
            True if the catalog item matches this filter, False otherwise
        """

        qn_tokens = data["q"].split(".")
        if len(qn_tokens) < 2:
            return False

        return (
            self._check_basic_info(data, qn_tokens)
            and self._check_quote_asset(data)
            and self._check_constant_tenor(data)
            and self._check_listing_filter(data)
        )


ChainCatalogFilters = dict[Network, list[FeedIndexerCatalogFilter]]

Exchange = str
BaseAsset = str
ParentAssetClasses = list[str]
BaseFeedMetaData = tuple[Exchange, BaseAsset, ParentAssetClasses]
