import asyncio
import copy as cp
import logging
from collections import Counter
from typing import ClassVar, cast

import utils_general
import utils_oracle
from block_stream.typings import CatalogData
from datagrabber import CatalogItem
from hexbytes import HexBytes
from utils_oracle import (
    CatalogToFeedProcessor,
    ConfigLoader,
    ConfigModel,
    Feed,
    FeedIndex,
    Network,
    generate_feed_keys_for_catalog_entries,
)
from utils_oracle.db.table_models import FeedState

from .catalog_filter_manager import CatalogFilterManager
from .database_handler import DatabaseHandler
from .exceptions import (
    InvalidChainState,
    MaxFeedIndexError,
)
from .feed_cleanup_manager import FeedCleanupManager
from .typings import (
    ChainFeedState,
    ChainToAuxDetails,
    FeedIndexerInternalFeeds,
    FeedKeyToIndexMap,
    Index,
)
from .utils import log_feed_state_summary


class FeedIndexer:
    # Definition of active feeds here is all feeds whose indices are NOT eligible for re-assignment
    _ACTIVE_FEEDS: ClassVar[tuple[FeedState, ...]] = tuple(
        feed_state
        for feed_state in FeedState.__members__.values()
        if feed_state != FeedState.CONFIRMED_REMOVE
    )

    def __init__(
        self,
        chain_aux_details: ChainToAuxDetails,
        catalog_to_feed_processor: type[
            CatalogToFeedProcessor
        ] = CatalogToFeedProcessor,
        catalog_filter_manager: CatalogFilterManager | None = None,
        feed_config: ConfigModel | None = None,
        database_handler: DatabaseHandler | None = None,
        feed_cleanup_manager: FeedCleanupManager | None = None,
        preloaded_internal_state: FeedIndexerInternalFeeds | None = None,
    ):
        if not preloaded_internal_state:
            logging.warning(
                "No FeedIndexer preloaded_internal_state set. Initializing with empty state"
            )

        self._feed_config = feed_config or ConfigLoader.get_feed_definitions()
        self._chain_aux_details = chain_aux_details

        self._database_handler = database_handler
        self._catalog_processor = catalog_to_feed_processor(
            self._feed_config
        ) or CatalogToFeedProcessor(self._feed_config)
        self._internal_state: FeedIndexerInternalFeeds = (
            preloaded_internal_state or utils_general.nested_dict()
        )

        if not feed_cleanup_manager:

            logging.warning(
                "No FeedCleanupManager provided. Initializing with default FeedCleanupManager"
            )
            assert (
                catalog_filter_manager
            ), "CatalogFilterManager must be provided if FeedCleanupManager is not"
            self._feed_cleanup_manager = FeedCleanupManager(
                feed_config=feed_config,
                catalog_filter_manager=catalog_filter_manager,
            )
        else:
            self._feed_cleanup_manager = feed_cleanup_manager

        self._state_lock = asyncio.Lock()

    async def process_record(
        self,
        record: CatalogData,
        chain_ids: list[Network],
    ) -> list[FeedIndex] | None:
        feed_indices: list[FeedIndex] = []

        r = _convert_to_instrument_details(record)

        chain_decimals_by_network: dict[Network, int] = {
            network: aux_details.decimals
            for network, aux_details in self._chain_aux_details.items()
        }

        for chain_id in chain_ids:
            version = self._chain_aux_details[chain_id].version
            chain_decimals = chain_decimals_by_network[chain_id]

            try:
                # Generate feeds for the current chain ID
                feeds = generate_feed_keys_for_catalog_entries(
                    self._catalog_processor, [r], chain_decimals
                )
            except Exception:
                logging.exception(
                    "Error while handling catalog entry for chain_id: %s, catalog record: %s",
                    chain_id,
                    r,
                )
                continue

            async with self._state_lock:
                for feed_key_to_feed in feeds.values():
                    for feed_key, feed in feed_key_to_feed.items():
                        if self._is_feed_already_indexed(
                            feed_key=feed_key,
                            chain_id=chain_id,
                            feed=feed,
                        ):
                            continue

                        try:
                            index = self._assign_index_for_feed(chain_id)
                        except MaxFeedIndexError:
                            logging.exception(
                                "Skipping index assignment for feed=%s, max_feed_index=%s",
                                feed,
                                self._chain_aux_details[
                                    chain_id
                                ].max_feed_index,
                            )
                            continue
                        except Exception:
                            logging.exception(
                                f"Error while assigning index for {chain_id}"
                            )
                            continue

                        feed_index = FeedIndex(
                            chain_id=chain_id,
                            feed=feed,
                            index=index,
                            version=version,
                        )

                        try:
                            self._add_feed_index_to_internal_state(
                                feed_index=feed_index,
                                chain_id=chain_id,
                                feed_key=feed_key,
                            )
                        except InvalidChainState:
                            logging.exception(
                                f"Error while adding feed index to internal state for {chain_id=}"
                            )
                            continue

                        feed_indices.append(feed_index)

        # outside of lock
        if feed_indices:
            await self._write_to_db(
                feed_indices=feed_indices,
                chain_decimals_by_network=chain_decimals_by_network,
            )
            return feed_indices

        else:
            return None

    def _is_feed_already_indexed(
        self,
        feed_key: HexBytes,
        chain_id: Network,
        feed: Feed,
    ) -> bool:
        """
        Checks if a given feed_key is already indexed.

        Args:
            feed_key (HexBytes): The key of the feed to check.
            active_feed_indices_by_state (ChainFeedState): The current active feed indices.
            chain_id (Network): The identifier of the chain.
            feed (Feed): The feed object.

        Returns:
            bool: True if the feed_key is already indexed, False otherwise.
        """
        active_feed_indices_by_state: ChainFeedState = (
            self._get_chain_feed_indices(chain_id, self._ACTIVE_FEEDS)
        )
        for state, feed_state in active_feed_indices_by_state.items():
            if feed_key in feed_state:
                feed_index = feed_state[feed_key]
                utils_general.log_bsdebug(
                    "Feed already indexed for chain_id=%s, feed_state=%s, FeedIndex=%s, feed_key=%s, feed=%s.",
                    chain_id,
                    state,
                    feed_index.log_index_details(),  # Log FeedIndex details
                    feed_key,
                    feed,
                )
                return True
        return False

    def _add_feed_index_to_internal_state(
        self,
        feed_key: HexBytes,
        feed_index: FeedIndex,
        chain_id: Network,
    ) -> None:
        """
        Adds a feed record to the internal state of a specified chain.

        This function updates the internal state by adding a feed record to the
        corresponding chain's feed state. If the chain's feed state does not exist,
        it retrieves it using `_get_chain_feed_state`. It will only ever add fees to the INITIALIZED_ADD state.

        NOTE: Callers should call this function with the self._state_lock as the internal state is modified

        :param feed_record: A dictionary-like object containing the feed information
                            to add. Must include the key ``feed_key``.
        :param chain_id: The identifier of the chain to which the feed record will
                         be added.
        """

        feed_index_by_state: ChainFeedState = self._get_chain_feed_indices(
            chain_id
        )
        all_keys = {
            key
            for feed_key_to_feeds in feed_index_by_state.values()
            for key in feed_key_to_feeds
        }
        if feed_key in all_keys:
            # Should not happen as we should have detected this when assigning a feed index
            raise InvalidChainState(
                f"Feed key record already exists. {chain_id=}, {feed_key=}, {feed_index=}"
            )

        feed_index_by_state[FeedState.INITIALISED_ADD][feed_key] = feed_index

    def _assign_index_for_feed(self, chain_id: Network) -> Index:
        """
        Assigns the next available index for a feed in the given chain.

        This function ensures that a unique index is assigned to a new feed within
        a chain, adhering to the maximum allowed index (`MAX_FEED_INDEX`). It verifies
        that no duplicate indices exist in the current chain feed state and finds
        the smallest unused index. If no feeds exist for the chain, it starts assigning
        indices from 0.

        NOTE: Callers should call this function with the self._state_lock as the decision on what index to assign
            is based on the internal state of the feed indexer.

        Args:
            chain_id (Network): The identifier for the chain whose feed is being indexed.

        :param chain_id: The identifier for the chain whose feed is being indexed.
        :return: The next available index for the feed, or `None` if no
                 state exists for the given chain.
        :raises InvalidChainState: If duplicate feed indices are detected in the chain feed state.
        :raises MaxFeedIndexError: If the maximum allowed index (`MAX_FEED_INDEX`) has
                                   been reached for the chain.
        """

        feed_index_by_state = self._get_chain_feed_indices(
            chain_id, self._ACTIVE_FEEDS
        )

        if not any(
            feed_key_to_index
            for feed_key_to_index in feed_index_by_state.values()
        ):
            logging.info(
                f"No indices found for {chain_id=}. Assigning index=0 for {chain_id=}"
            )
            return 0

        all_assigned_indices = {
            fi.index
            for chain_feed_state in feed_index_by_state.values()
            for fi in chain_feed_state.values()
        }
        index = 0
        # find the smallest index that has yet to be assigned
        while index in all_assigned_indices:
            index += 1

        if index > self._chain_aux_details[chain_id].max_feed_index:
            raise MaxFeedIndexError(
                f"Maximum Index reached for {chain_id=}, {self._chain_aux_details[chain_id].max_feed_index=}"
            )

        logging.info(f"Assigning {index=} for {chain_id=}")
        return index

    def _get_chain_feed_indices(
        self, chain_id: Network, states: tuple[FeedState, ...] | None = None
    ) -> ChainFeedState:
        """
        Retrieves the feed state for a specific chain and filters by the provided states.

        If no states are provided, returns the full state.

        :param chain_id: The identifier of the chain whose feed state is being retrieved.
        :param states: A list of states to filter the feed state. If empty, returns the full state.
        :return: The filtered or full feed state for the specified chain.
        """
        chain_feed_state = self._internal_state[chain_id]
        if not chain_feed_state:
            logging.warning(
                f"No feed indices found in internal state for {chain_id=}"
            )
            return chain_feed_state

        if not states:
            return chain_feed_state

        internal_state: ChainFeedState = utils_general.nested_dict()
        for state in states:
            internal_state[state] = chain_feed_state[
                state
            ]  # Keep the reference to the original defaultdict

        self._validate_chain_feed_state(internal_state)
        return internal_state

    async def get_chain_feed_indices(
        self,
        chain_id: Network,
        states: tuple[FeedState, ...] | None = None,
    ) -> ChainFeedState:
        """
        Acquire the lock, read and copy the feed indices for the given chain & states,
        then return the copy. The caller does NOT need to lock externally. Use this function
        when we want to read the state of the internal state, without writing it
        """
        async with self._state_lock:
            chain_feed_state = self._get_chain_feed_indices(chain_id, states)
            chain_feed_state_copy = cp.deepcopy(chain_feed_state)
            return chain_feed_state_copy

    def _validate_chain_feed_state(
        self, chain_feed_state: ChainFeedState
    ) -> None:
        """
        Validates that a ChainFeedState does not contain duplicate indices.

        This function checks that all feed indices in the given ChainFeedState are unique.
        If duplicates are found, it raises an `InvalidChainState`.

        :param chain_feed_state: The feed state to validate, expected to
                                     be a dictionary-like object where each
                                     value contains an "index" key.
        :raises InvalidChainState: If duplicate indices are found in the ChainFeedState.

        """
        all_indices = []
        for _state, key_to_feed_indices in chain_feed_state.items():
            all_indices.extend(
                [record.index for record in key_to_feed_indices.values()]
            )

        index_counts = Counter(all_indices)
        duplicate_indices = {
            index for index, count in index_counts.items() if count > 1
        }

        if duplicate_indices:
            raise InvalidChainState(
                f"Duplicate indices found in ChainFeedState: {duplicate_indices=}"
            )

    async def get_feeds_to_remove_for_chain(
        self,
        chain_id: Network,
    ) -> FeedKeyToIndexMap:
        """
        Get expired feed records for a specific chain ID.
        Only feeds in the CONFIRMED_ADD state are eligible for removal

        :param chain_id: The chain ID to process.
        :return: A dictionary of `FeedKeyRecord` to remove.

        """

        chain_feed_state = await self.get_chain_feed_indices(
            chain_id, (FeedState.CONFIRMED_ADD,)
        )
        confirmed_feeds = chain_feed_state[FeedState.CONFIRMED_ADD]
        chain_decimals = self._chain_aux_details[chain_id].decimals

        # Use the feed cleanup manager to determine which feeds should be removed
        return await self._feed_cleanup_manager.get_feeds_to_remove_for_chain(
            chain_id=chain_id,
            chain_decimals=chain_decimals,
            confirmed_feeds=confirmed_feeds,
        )

    async def clean_up_feeds_for_chain(
        self,
        feed_keys_to_remove: list[HexBytes],
        chain_id: Network,
        chain_decimal: int,
    ) -> None:
        """
        Moves feed indices from CONFIRMED_ADD to INITIALISED_REMOVE for the specified feed keys.

        Reads and writes to the FeedIndexer internal state so needs to acquire lock
        """
        async with self._state_lock:
            feed_index_by_state = self._get_chain_feed_indices(
                chain_id,
                (FeedState.CONFIRMED_ADD, FeedState.INITIALISED_REMOVE),
            )
            confirmed_feed_indices = feed_index_by_state[
                FeedState.CONFIRMED_ADD
            ]
            initialised_remove_feed_indices = feed_index_by_state[
                FeedState.INITIALISED_REMOVE
            ]

            # for logging
            successful_key_to_feed_sting: dict[str, str] = {}
            failed_key_to_feed_sting: dict[str, str] = {}
            for feed_key in feed_keys_to_remove:
                if feed_key in confirmed_feed_indices:
                    feed_index = confirmed_feed_indices[feed_key]
                    decoded_feed = confirmed_feed_indices[
                        feed_key
                    ].feed.get_decoded_feed()

                    # Remove from CONFIRMED_ADD
                    del confirmed_feed_indices[feed_key]

                    # Move into INITIALISED_REMOVE
                    initialised_remove_feed_indices[feed_key] = feed_index

                    successful_key_to_feed_sting[feed_key.hex()] = (
                        decoded_feed.feed_to_string(
                            chain_decimals=chain_decimal
                        )
                    )
                else:
                    failed_key_to_feed_sting[feed_key.hex()] = (
                        decoded_feed.feed_to_string(
                            chain_decimals=chain_decimal
                        )
                    )

        if successful_key_to_feed_sting:
            logging.info(
                "Moved feeds from state=%s to state=%s for chain_id=%d, key=%s",
                FeedState.CONFIRMED_ADD,
                FeedState.INITIALISED_REMOVE,
                chain_id,
                successful_key_to_feed_sting,
            )
        if failed_key_to_feed_sting:
            logging.error(
                "Feeds not found in the state=%s state for chain_id=%s; skipping. failed_keys=%s",
                FeedState.CONFIRMED_ADD,
                chain_id,
                failed_key_to_feed_sting,
            )
        return

    async def _write_to_db(
        self,
        feed_indices: list[FeedIndex],
        chain_decimals_by_network: dict[Network, int],
    ) -> None:
        if self._database_handler:
            await self._database_handler.write_feed_indices(
                feed_indices, chain_decimals_by_network
            )
        else:
            logging.error(
                "No database handler available to write to the database"
            )

    def get_max_feed_index_for_chains(
        self, chain_ids: list[Network]
    ) -> dict[tuple[str, ...], float]:
        metrics: dict[tuple[str, ...], float] = {}

        for chain_id in chain_ids:
            chain_name = str(chain_id.name)
            feed_index_by_state = self._get_chain_feed_indices(chain_id)
            feed_indices = (
                fi
                for feed_state in feed_index_by_state.values()
                for fi in feed_state.values()
            )
            metrics[(chain_name,)] = float(
                max((fi.index for fi in feed_indices), default=0)
            )

        return metrics

    def get_active_feed_indices_for_chains(
        self, chain_ids: list[Network]
    ) -> dict[tuple[str, ...], float]:
        metrics: dict[tuple[str, ...], float] = {}

        for chain_id in chain_ids:
            chain_name = str(chain_id.name)
            # Active feeds here are defined as feeds that the feed indexer has assigned and considers active and not
            # what ends up on Chain. This metric is used to track the FI's view of indices and not the chains which can
            # monitored separately.
            feed_index_by_state = self._get_chain_feed_indices(
                chain_id, self._ACTIVE_FEEDS
            )
            metrics[(chain_name,)] = float(
                sum(
                    len(feed_state)
                    for feed_state in feed_index_by_state.values()
                )
            )

        return metrics

    async def replace_feed_states_with_db(self) -> None:
        """
        Replaces our entire internal state by reading from the database.
        To avoid partial reads/writes, we do everything inside one state_lock block.
        """
        if not self._database_handler:
            raise Exception(
                "No database handler set. Cannot sync feed states with DB."
            )

        # The FI considers all feeds in its internal state as active. We skip loading these indices so that
        # we can re-use the indices we know we have removed. Also this approach is more memory efficient
        active_states = list(self._ACTIVE_FEEDS)
        async with self._state_lock:
            logging.info("Syncing feed states with DB for all states")
            new_internal_state: FeedIndexerInternalFeeds = (
                utils_general.nested_dict()
            )

            for chain_id, aux_details in self._chain_aux_details.items():
                chain_version = aux_details.version

                state_to_feed_indices = await self._database_handler.get_feed_indices_by_state_for_chain_id(
                    chain_id=chain_id,
                    version=chain_version,
                    states=active_states,
                )

                if state_to_feed_indices:
                    for (
                        feed_state,
                        feed_indices,
                    ) in state_to_feed_indices.items():
                        for feed_index in feed_indices:
                            # todo: update so that we do not have to generate feed_key each time
                            feed_key = utils_oracle.get_feed_key(
                                feed_index.feed
                            )
                            new_internal_state[chain_id][feed_state][
                                feed_key
                            ] = feed_index

            # Atomically replace the entire internal state
            self._internal_state = new_internal_state
            log_feed_state_summary(self._internal_state)

        return


def _convert_to_instrument_details(
    catalog_data: CatalogData,
) -> CatalogItem:
    catalog_data = cp.copy(catalog_data)
    converted = cast(dict[str, object], catalog_data)
    converted["qualified_name"] = converted["q"]
    converted["instrument_name"] = converted["instrument"]
    del converted["q"]
    return cast(CatalogItem, converted)
